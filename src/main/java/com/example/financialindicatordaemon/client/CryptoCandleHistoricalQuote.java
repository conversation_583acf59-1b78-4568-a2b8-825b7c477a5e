package com.example.financialindicatordaemon.client;

public class CryptoCandleHistoricalQuote {
    private String timeOpen;
    private String timeClose;
    private String timeHigh;
    private String timeLow;
    private Quote quote;

    public String getTimeOpen() {
        return timeOpen;
    }

    public void setTimeOpen(String timeOpen) {
        this.timeOpen = timeOpen;
    }

    public String getTimeClose() {
        return timeClose;
    }

    public void setTimeClose(String timeClose) {
        this.timeClose = timeClose;
    }

    public String getTimeHigh() {
        return timeHigh;
    }

    public void setTimeHigh(String timeHigh) {
        this.timeHigh = timeHigh;
    }

    public String getTimeLow() {
        return timeLow;
    }

    public void setTimeLow(String timeLow) {
        this.timeLow = timeLow;
    }

    public Quote getQuote() {
        return quote;
    }

    public void setQuote(Quote quote) {
        this.quote = quote;
    }

    public static class Quote {
        private String name;
        private Double open;
        private Double high;
        private Double low;
        private Double close;
        private Double volume;
        private Double marketCap;
        private String timestamp;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Double getOpen() {
            return open;
        }

        public void setOpen(Double open) {
            this.open = open;
        }

        public Double getHigh() {
            return high;
        }

        public void setHigh(Double high) {
            this.high = high;
        }

        public Double getLow() {
            return low;
        }

        public void setLow(Double low) {
            this.low = low;
        }

        public Double getClose() {
            return close;
        }

        public void setClose(Double close) {
            this.close = close;
        }

        public Double getVolume() {
            return volume;
        }

        public void setVolume(Double volume) {
            this.volume = volume;
        }

        public Double getMarketCap() {
            return marketCap;
        }

        public void setMarketCap(Double marketCap) {
            this.marketCap = marketCap;
        }

        public String getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(String timestamp) {
            this.timestamp = timestamp;
        }

    }

}
