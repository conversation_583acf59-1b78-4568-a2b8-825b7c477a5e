package com.example.financialindicatordaemon.mapper;

import com.example.financialindicatordaemon.client.CryptoCandleHistoricalQuote;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CmcCandleDataMapper {

    void insert(@Param("cryptoCurrencySymbol") String cryptoCurrencySymbol,
                @Param("conversionCurrency") String conversionCurrency,
                @Param("quotes") List<CryptoCandleHistoricalQuote> quotes);

    List<CryptoCandleHistoricalQuote> findBySymbolAndConversionCurrency(
            @Param("cryptoCurrencySymbol") String cryptoCurrencySymbol,
            @Param("conversionCurrency") String conversionCurrency);

}
