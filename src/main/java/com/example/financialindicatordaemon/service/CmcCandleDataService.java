package com.example.financialindicatordaemon.service;

import com.example.financialindicatordaemon.client.CryptoCandleHistoricalQuote;
import com.example.financialindicatordaemon.mapper.CmcCandleDataMapper;
import org.springframework.stereotype.Component;

import java.util.List;

import static org.apache.commons.collections4.ListUtils.partition;

@Component
public class CmcCandleDataService {

    private CmcCandleDataMapper cmcCandleDataMapper;

    public CmcCandleDataService(CmcCandleDataMapper cmcCandleDataMapper) {
        this.cmcCandleDataMapper = cmcCandleDataMapper;
    }

    public void insert(String cryptoCurrencySymbol,
                       String conversionCurrency,
                       List<CryptoCandleHistoricalQuote> quotes) {
        partition(quotes, 2000).forEach(quoteList -> {
            cmcCandleDataMapper.insert(cryptoCurrencySymbol, conversionCurrency, quoteList);
        });
    }

    public List<CryptoCandleHistoricalQuote> find(String cryptoCurrencySymbol, String conversionCurrency) {
        return cmcCandleDataMapper.findBySymbolAndConversionCurrency(cryptoCurrencySymbol, conversionCurrency);
    }

}
