<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.financialindicatordaemon.mapper.CmcCandleDataMapper">

    <insert id="insert">
        INSERT INTO crypto_data.cmc_candle_data (symbol,
        conversion_currency,
        time_open,
        time_close,
        time_high,
        time_low,
        quote)
        VALUES
        <foreach collection="quotes" item="quote" separator=",">
            (#{cryptoCurrencySymbol},
            #{conversionCurrency},
            #{quote.timeOpen}::timestamp,
            #{quote.timeClose}::timestamp,
            #{quote.timeHigh}::timestamp,
            #{quote.timeLow}::timestamp,
            #{quote.quote})
        </foreach>
    </insert>
    <select id="findBySymbolAndConversionCurrency"
            resultType="com.example.financialindicatordaemon.client.CryptoCandleHistoricalQuote">
        SELECT *
        FROM crypto_data.cmc_candle_data
        WHERE symbol = #{cryptoCurrencySymbol}
          AND conversion_currency = #{conversionCurrency}
    </select>
</mapper>
