package com.example.financialindicatordaemon.service;

import com.example.financialindicatordaemon.BaseTest;
import com.example.financialindicatordaemon.client.CryptoCandleHistoricalQuotes;
import com.example.financialindicatordaemon.client.CryptocurrencyMapping;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;


public class CoinMarketCapServiceTest extends BaseTest {

    @Autowired
    private CoinMarketCapService coinMarketCapService;

    @Test
    public void getCryptocurrencyMappings_shouldReturnMappings() {
        List<CryptocurrencyMapping> mappings = coinMarketCapService.getCryptocurrencyMappings();
        assertThat(mappings).isNotEmpty();
    }

    @Test
    public void findHistoricalQuotes_shouldReturnQuotes() {
        String start = "1579110719";
        String end = "1594662719";
        assertThat(Integer.parseInt(end) - Integer.parseInt(start)).isEqualTo(15552000);

        CryptoCandleHistoricalQuotes quotes = coinMarketCapService.findHistoricalQuotes(5426, 1, start, end);
        assertThat(quotes).isNotNull();
        assertThat(quotes.getQuotes()).isNotEmpty();
        assertThat(quotes.getQuotes()).hasSize(180);
    }

}
